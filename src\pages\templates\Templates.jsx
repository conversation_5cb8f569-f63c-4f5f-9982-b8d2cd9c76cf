import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useNavigate, useParams } from "react-router-dom";
import TemplateCard from "@/components/ui/TemplateCard";
import Confirm from "@/components/ui/Confirm";
import { toast } from "sonner";
import { Trash2, Eye, Edit, Edit3 } from "lucide-react";
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import Modal from "@/components/ui/Modal";
import TemplateForm from "./TemplateForm";

const Templates = () => {
  const { id } = useParams();
  const [page, setPage] = useState(1);
  const [perPage] = useState(10);
  const [search, setSearch] = useState("");
  const navigate = useNavigate();
  const [selectedBook, setSelectedBook] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [allTemplates, setAllTemplates] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const {
    data: books,
    isLoading,
    refetch,
    error,
  } = useDataFetching({
    queryKey: ["templates", page, perPage, search],
    endPoint: "/admin/templates",
    params: { page, per_page: perPage, search },
  });

  // Reset page when search changes
  useEffect(() => {
    setPage(1);
    setAllTemplates([]);
    setHasMore(true);
  }, [search]);

  // Handle data accumulation for "Show More" functionality
  useEffect(() => {
    if (books?.data?.data) {
      if (page === 1) {
        // First page or new search - replace all templates
        setAllTemplates(books.data.data);
      } else {
        // Subsequent pages - append to existing templates
        setAllTemplates(prev => [...prev, ...books.data.data]);
      }
      // Update hasMore based on pagination info
      setHasMore(page < books.data.total_pages);
    }
  }, [books, page]);

  useEffect(() => {
    if (error) {
      toast.error("Failed to fetch templates. Please try again.");
    }
  }, [error]);

  const handleModalClose = () => {
    setIsEditModalOpen(false);
    setSelectedBook(null); // Reset selected book
  };

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  const handleDelete = (e, bookId) => {
    Confirm(
      async () => {
        try {
          await api.delete(`/admin/templates/${bookId}`);
          refetch();
        } catch (error) {
          console.error("Error deleting template:", error);
        }
      },
      "Are you sure you want to delete this template?",
      "This template",
      "will be deleted permanently.",
      "Cancel",
      "Delete"
    );
  };

  const handleView = (book) => {
    navigate(`/templates/${book.id}`);
  };

  const handleUse = (book) => {
    api
      .post(`admin/ebooks/use-templates`, {
        ebook_id: id,
        template_id: book.id,
      })
      .then(() => {
        refetch();
      })
      .catch(() => {
        toast.error("Error using template");
      });
  };

  const handleEditBasicInfo = (book) => {
    setSelectedBook(book);
    setIsEditModalOpen(true);
  };

  const generateMenuItems = (book) => [
    {
      label: "View",
      action: () => handleView(book),
      icon: <Eye size={16} />,
    },
    {
      label: "Edit",
      action: () => navigate(`/templates/edit/${book.id}`),
      icon: <Edit size={16} />,
    },
    {
      label: "Edit Basic Info",
      action: () => handleEditBasicInfo(book),
      icon: <Edit3 size={16} />,
    },
    {
      label: "Delete",
      action: (e) => handleDelete(e, book.id),
      icon: <Trash2 size={16} />,
      danger: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white p-6 dark:bg-[#111827]">
      <h3 className="text-2xl font-bold mb-4 dark:text-white">Templates</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {isLoading && page === 1 ? (
          Array.from({ length: 5 }, (_, index) => (
            <EbookCardSkeleton key={index} />
          ))
        ) : allTemplates.length > 0 ? (
          allTemplates.map((book) => (
            <TemplateCard
              key={book.id}
              book={book}
              onView={() => handleView(book)}
              menuItems={generateMenuItems(book)}
              // onUse={() => handleUse(book)}
            />
          ))
        ) : (
          <div className="col-span-full text-center text-gray-500 dark:text-gray-400">
            No templates found.
          </div>
        )}
      </div>

      {/* Show More Button */}
      {hasMore && allTemplates.length > 0 && (
        <div className="flex justify-center mt-8">
          <button
            onClick={handleLoadMore}
            disabled={isLoading}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
          >
            {isLoading ? 'Loading...' : 'Show More'}
          </button>
        </div>
      )}
      <Modal
        activeModal={isEditModalOpen}
        onClose={handleModalClose}
        title={selectedBook ? "Edit Book Basic Information" : "Create New Book"}
        className="w-full max-w-3xl"
      >
        <TemplateForm
          handleModalClose={handleModalClose}
          initialValues={selectedBook}
        />
      </Modal>
    </div>
  );
};

export default Templates;
