import { useMutation } from '@tanstack/react-query';
import api from '../lib/axios';
import { setToken, removeToken, setUser } from '../lib/auth';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { login as loginAction, logout as logoutAction } from '../store/authSlice';
import axios from 'axios';
import { toast } from "sonner";

export const useAuth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const loginMutation = useMutation({
    mutationFn: async (credentials) => {
      const { data } = await axios.post(`${import.meta.env.VITE_HOST_URL}/api/login`, credentials);
      return data;
    },
    onSuccess: (data) => {
      // Set auth state
      setToken(data.token);
      setUser(data.user);
      dispatch(loginAction({ token: data.token, user: data.user }));
      
      // Redirect logic
      const queryParams = new URLSearchParams(location.search);
      const redirectPath = queryParams.get('redirect');
      const fromRoute = redirectPath || location.state?.from?.pathname || '/dashboard';
      
      navigate(fromRoute, { replace: true });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Login failed. Please try again.');
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (userData) => {
      const { data } = await api.post('/register', userData);
      return data;
    },
    onSuccess: (data) => {
      // Set auth state
      setToken(data.token);
      setUser(data.user);
      dispatch(loginAction({ token: data.token, user: data.user }));

      // Redirect logic
      const queryParams = new URLSearchParams(location.search);
      const redirectPath = queryParams.get('redirect');
      const fromRoute = location.state?.from?.pathname || redirectPath || '/';

      navigate(fromRoute, { replace: true });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Registration failed. Please try again.');
    },
  });

  const logout = () => {
    removeToken();
    setUser(null);
    dispatch(logoutAction());
    
    toast.info('Logged out successfully.');
    navigate('/login', { replace: true });
  };

  return {
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout,
    isLoading: loginMutation.isPending || registerMutation.isPending,
    error: loginMutation.error || registerMutation.error,
  };
};
