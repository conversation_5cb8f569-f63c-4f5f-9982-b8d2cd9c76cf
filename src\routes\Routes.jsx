import React, { Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import ManageOrders from "../pages/orders/ManageOrders";
import PrivateRoute from "../components/PrivateRoute";
import Loading from "../components/Loading";

// Layouts
const Layout = React.lazy(() => import("../components/layouts/Layout"));
const DashboardLayout = React.lazy(() => import("../components/layouts/DashboardLayout"));

// Auth pages
const Login = React.lazy(() => import("../pages/Login"));
const Register = React.lazy(() => import("../pages/Register"));
const AccessDenied = React.lazy(() => import("../pages/AccessDenied"));

// Public pages
const Home = React.lazy(() => import("@/pages/home/<USER>"));
const AllBooks = React.lazy(() => import("../pages/home/<USER>"));
const NotFound = React.lazy(() => import("../pages/NotFound"));

// Book preview pages
const BookPreview = React.lazy(() => import("../pages/book-preview/BookPreview"));
const BookScrollView = React.lazy(() => import("../pages/book-preview/BookScrollView"));
const TemplatePreview = React.lazy(() => import("../pages/book-preview/TemplatePreview"));

// Editor pages
const Editor = React.lazy(() => import("../pages/editor/Editor"));
const BlankEditor = React.lazy(() => import("../pages/editor/BlankEditor"));
const EditTemplate = React.lazy(() => import("../pages/templates/EditTemplate"));

// Admin dashboard pages
const Dashboard = React.lazy(() => import("../pages/Dashboard"));
const Categories = React.lazy(() => import("../pages/categories/Categories"));
const EbookList = React.lazy(() => import("../pages/ebooks/EbookList"));
const ManageEbooks = React.lazy(() => import("../pages/ebooks/ManageEbooks"));
const Templates = React.lazy(() => import("../pages/templates/Templates"));
const UsersList = React.lazy(() => import("../pages/users/UsersList"));
const Profile = React.lazy(() => import("../pages/settings/Profile"));
const Security = React.lazy(() => import("../pages/settings/Security"));
const Inquiries = React.lazy(() => import("../pages/inquiries/Inquiries"));
const Authors = React.lazy(() => import("../pages/authors/Authors"));
const CouponList = React.lazy(() => import("../pages/coupons/CouponList"));
const PublishersList = React.lazy(() => import("../pages/publishers/PublishersList"));
const StaticPages = React.lazy(() => import("../pages/staticPages/StaticPages"));
const OrderDetails = React.lazy(() => import("../pages/orders/OrderDetails"));

const AppRoutes = () => {
  return (
    <Suspense fallback={<Loading />}>
      <Routes>
        {/* Public Routes */}
        <Route element={<Layout />}>
          <Route path="/" element={<Home />} />
          <Route path="ebooks" element={
            <div className="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-40">
              <AllBooks />
            </div>
          } />
          <Route path="editor/:stepId/:bookId" element={
            <PrivateRoute element={BlankEditor} allowedRoles={["admin", "user"]} />
          } />
        </Route>


        {/* Auth Routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="unauthorized" element={<AccessDenied />} />

        {/* Book Preview Routes */}
        {/* <Route path="flipbooks/:slug" element={<BookPreview />} /> */}
        <Route path="flipbooks/:id" element={<BookPreview />} />
        {/* <Route path="ebooks/:slug" element={<BookScrollView />} /> */}
        <Route path="ebooks/:id" element={<BookScrollView />} />

        {/* Protected Editor Routes */}
        <Route path="/ebooks/edit/:id" element={
          <PrivateRoute element={Editor} allowedRoles={["admin", "user", "publisher", "author"]} />
        } />
        <Route path="/templates/edit/:id" element={
          <PrivateRoute element={EditTemplate} allowedRoles={["admin", "user"]} />
        } />
        <Route path="/templates/:id" element={
          <PrivateRoute element={TemplatePreview} allowedRoles={["admin", "user"]} />
        } />

        {/* Dashboard Routes - Common for all roles */}
        <Route path="/dashboard/*" element={
          <PrivateRoute element={DashboardLayout} allowedRoles={["admin", "publisher", "author", "user"]} />
        }>
          <Route path="" element={<Dashboard />} />
          <Route path="categories" element={<Categories />} />
          <Route path="ebooks" element={<EbookList />} />
          <Route path="templates" element={<Templates />} />
          <Route path="users" element={<UsersList />} />
          <Route path="settings/profile" element={<Profile />} />
          <Route path="inquiries" element={<Inquiries />} />
          <Route path="authors" element={<Authors />} />
          <Route path="coupons" element={<CouponList />} />
          <Route path="publishers" element={<PublishersList />} />
          <Route path="pending-ebooks" element={<ManageEbooks />} />
          <Route path="static-pages" element={<StaticPages />} />
          <Route path="orders" element={<ManageOrders />} />
          <Route path="orders/:orderId" element={<OrderDetails />} />
          <Route path="*" element={<NotFound />} />
        </Route>

        {/* Catch-all Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
