import React, { useState } from 'react';
import { Eye, MoreVertical } from 'lucide-react';
import noImage from '@/assets/noImage.png';

const EbookCard = ({ book, onView, menuItems = [], isPdf }) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const hasActions = Boolean(onView) || (Array.isArray(menuItems) && menuItems.length > 0);

  return (
    <div
      className="relative group bg-white rounded-lg shadow-md"
      onMouseEnter={() => setShowOverlay(hasActions)}
      onMouseLeave={() => {
        setShowOverlay(false);
        setShowMenu(false);
      }}
    >
      <div className="relative" style={{ width: '100%', height: '250px' }}>
        {isPdf && (
          <div className="absolute top-2 left-2 z-10 bg-blue-500 text-white text-xs font-semibold px-2 py-1 rounded">
            PDF
          </div>
        )}
        <img
          src={book.cover_image ? `${import.meta.env.VITE_HOST_URL}/storage/${book.cover_image}` : noImage}
          alt={book.title}
          style={{
            height: '100%',
            width: '100%',
            objectFit: 'contain',
            padding: '10px'
          }}
          onClick={onView}
        />
        <div className={`absolute inset-0 bg-gray-500 transition-opacity duration-300 flex items-start justify-end p-2 ${
          showOverlay ? 'bg-opacity-40 opacity-100' : 'bg-opacity-0 opacity-0 pointer-events-none'
        }`}>
          <div className="flex space-x-2">
            {onView && (
              <button
                onClick={onView}
                className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200"
                aria-label="View"
              >
                <Eye className="w-5 h-5 text-gray-800" />
              </button>
            )}
            {Array.isArray(menuItems) && menuItems.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setShowMenu(!showMenu)}
                  className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200"
                  aria-label="More options"
                >
                  <MoreVertical className="w-5 h-5 text-gray-800" />
                </button>
                {showMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10" style={{ right: 0, top: '100%' }}>
                    <div className="py-1">
                      {menuItems.map((item, index) => (
                        <React.Fragment key={index}>
                          <button
                            onClick={() => {
                              item.action();
                              setShowMenu(false); // Close menu after action
                            }}
                            className={`flex items-center w-full text-left px-4 py-2 text-sm ${item.danger ? 'text-red-600' : 'text-gray-700'} hover:bg-gray-100`}
                          >
                            {item.icon && <span className="mr-2">{item.icon}</span>}
                            {item.label}
                          </button>
                          {index < menuItems.length - 1 && (
                            <hr className="my-1 border-t border-gray-200 w-full" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="p-4" style={{ textAlign: 'center', paddingTop: 0 }}>
        <h3 className="font-semibold text-gray-800 truncate">{book.title}</h3>
        <p className="text-sm text-gray-600 mt-1">{book.author}</p>
      </div>
    </div>
  );
};

export default EbookCard;
