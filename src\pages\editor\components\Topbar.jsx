import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Moon, Sun, Code, Bell, ChevronDown, Eye, EyeOff } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import { useTheme } from "../hooks/useTheme";
import { setIsPreviewActive } from "../store/toolbarSlice";
import { Icon } from "@iconify/react";
import logo from "@/assets/ebookLogo.png";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/hooks/useAuth";
import MenuBar from "./MenuBar";

const Modal = ({ code, onClose }) => (
  <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-2/3 max-w-2xl">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold dark:text-white">Code Export</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <Icon icon="mdi:close" className="w-6 h-6" />
        </button>
      </div>
      <div className="flex justify-between items-center mb-4">
        <button
          onClick={() =>
            navigator.clipboard.writeText(JSON.stringify(code, null, 2))
          }
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <Icon icon="mdi:content-copy" className="w-6 h-6" />
        </button>
      </div>
      <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-md overflow-auto max-h-96">
        <code className="text-sm dark:text-gray-200">
          {JSON.stringify(code, null, 2)}
        </code>
      </pre>
    </div>
  </div>
);

const Topbar = () => {
  const [showModal, setShowModal] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const profileMenuRef = useRef(null);
  const pages = useSelector((state) => state.pages);
  const { theme, toggleTheme } = useTheme();
  const isPreview = useSelector((state) => state.toolbar.isPreviewActive);
  const user = useSelector((state) => state.auth.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        profileMenuRef.current &&
        !profileMenuRef.current.contains(event.target)
      ) {
        setProfileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="flex justify-between items-center px-6 py-3 border-b bg-white dark:bg-gray-800 dark:border-gray-600 transition-colors">
      <div
        className="flex items-center space-x-2 cursor-pointer"
        onClick={() => navigate("/dashboard")}
      >
        <img src={logo} alt="Logo" className="h-8" />
      </div>

      <div className="flex items-center space-x-3">
        <MenuBar />
        <button
          onClick={toggleTheme}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          aria-label="Toggle theme"
        >
          {theme === "dark" ? (
            <Sun className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          ) : (
            <Moon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          )}
        </button>
        <button
          onClick={() => dispatch(setIsPreviewActive(!isPreview))}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          aria-label="Preview"
        >
          {isPreview ? (
            <EyeOff className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          ) : (
            <Eye className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          )}
          <span className="sr-only">
            {isPreview ? "Exit Preview" : "Enter Preview"}
          </span>
        </button>

        <div className="flex items-center space-x-4 ml-4 border-l pl-4 dark:border-gray-600">
          <div className="relative" ref={profileMenuRef}>
            <button
              onClick={() => setProfileMenuOpen((prev) => !prev)}
              className="flex items-center space-x-2 px-4 py-2 rounded text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-300 flex items-center justify-center">
                {user && user.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-gray-600 text-sm font-medium">
                    {user?.name?.charAt(0).toUpperCase() || "U"}
                  </span>
                )}
              </div>
              <span>{user?.name || "User"}</span>
              <ChevronDown
                className={`w-4 h-4 transition-transform ${
                  profileMenuOpen ? "rotate-180" : ""
                }`}
              />
            </button>
            <AnimatePresence>
              {profileMenuOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute right-0 top-14 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg rounded-lg overflow-hidden z-[9999]"
                >
                  <Link
                    to="/dashboard"
                    className="block px-4 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 no-underline hover:no-underline"
                  >
                    {t("Dashboard")}
                  </Link>
                  <Link
                    to="/dashboard/settings/profile"
                    className="block px-4 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 no-underline hover:no-underline"
                  >
                    {t("Profile")}
                  </Link>

                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    {t("Logout")}
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {showModal && <Modal code={pages} onClose={() => setShowModal(false)} />}
    </div>
  );
};

export default Topbar;
