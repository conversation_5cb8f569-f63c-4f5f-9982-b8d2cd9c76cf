import {
  BookOpen,
  FileText,
  Home,
  Mail,
  Settings,
  Tag,
  Users,
  Ticket,
  ShoppingCart,
} from "lucide-react";

// Menu items with role-based filtering
const baseMenuItems = [
  {
    title: "Dashboard",
    icon: <Home className="w-5 h-5" />,
    path: "/dashboard",
    roles: ["admin", "publisher", "author", "user"],
  },
  {
    title: "eBook",
    icon: <FileText className="w-5 h-5" />,
    path: "/dashboard/ebooks",
    roles: ["admin", "publisher", "author", "user"],
  },
  {
    title: "Manage eBooks",
    icon: <FileText className="w-5 h-5" />,
    path: "/dashboard/pending-ebooks",
    roles: ["admin", "publisher"],
  },
  {
    title: "Templates",
    icon: <BookOpen className="w-5 h-5" />,
    path: "/dashboard/templates",
    roles: ["admin", "user"],
  },
  {
    title: "Categories",
    icon: <Tag className="w-5 h-5" />,
    path: "/dashboard/categories",
    roles: ["admin"],
  },
  {
    title: "Coupons",
    icon: <Ticket className="w-5 h-5" />,
    path: "/dashboard/coupons",
    roles: ["admin"],
  },
  {
    title: "Publishers",
    icon: <Mail className="w-5 h-5" />,
    path: "/dashboard/publishers",
    roles: ["admin"],
  },
  {
    title: "Authors",
    icon: <Users className="w-5 h-5" />,
    path: "/dashboard/authors",
    roles: ["admin", "publisher"],
  },
  {
    title: "Users",
    icon: <Users className="w-5 h-5" />,
    path: "/dashboard/users",
    roles: ["admin"],
  },
  {
    title: "Inquiries",
    icon: <Mail className="w-5 h-5" />,
    path: "/dashboard/inquiries",
    roles: ["admin"],
  },
  {
    title: "Orders",
    icon: <ShoppingCart className="w-5 h-5" />,
    path: "/dashboard/orders",
    roles: ["admin", "publisher"],
  },
  {
    title: "Settings",
    icon: <Settings className="w-5 h-5" />,
    roles: ["admin", "publisher", "author", "user"],
    submenu: [
      { title: "Profile", path: "/dashboard/settings/profile", roles: ["admin", "publisher", "author", "user"] },
      {
        title: "Static Pages",
        icon: <Users className="w-5 h-5" />,
        path: "/dashboard/static-pages",
        roles: ["admin"],
      },
    ],
  },
];

// Generate menu items filtered by user role
export const getMenuItems = (userRole) => {
  return baseMenuItems
    .filter(item => item.roles.includes(userRole))
    .map(item => ({
      ...item,
      submenu: item.submenu
        ? item.submenu.filter(subItem => subItem.roles.includes(userRole))
        : undefined,
    }));
};

// Export all menu items (for backward compatibility)
export const menuItems = baseMenuItems;

export const sidebarVariants = {
  open: {
    width: "16rem",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
  closed: {
    width: "4rem",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
};

export const submenuVariants = {
  open: {
    height: "auto",
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  closed: {
    height: 0,
    opacity: 0,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
};

export const profileMenuVariants = {
  open: { opacity: 1, scale: 1, transition: { duration: 0.2 } },
  closed: { opacity: 0, scale: 0.95, transition: { duration: 0.2 } },
};

export const formattedDate = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  return `${month} ${day}, ${year}`;
};